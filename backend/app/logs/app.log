2025-08-20 21:42:39 | INFO     | app.core.logging:setup_logging:96 | 日志系统初始化完成
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/images
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/yaml
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: uploads/playwright
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: logs
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: static
2025-08-20 21:42:39 | INFO     | app.utils.file_utils:ensure_directories:31 | 确保目录存在: screenshots
2025-08-20 21:42:39 | INFO     | app.main:validate_system_config:142 | 验证系统配置...
2025-08-20 21:42:39 | INFO     | app.core.llms:get_model_config_status:100 | AI模型配置状态: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:42:39 | INFO     | app.main:validate_system_config:151 | ✅ AI模型配置: {'deepseek_configured': True, 'openai_configured': True, 'qwen_vl_configured': True, 'ui_tars_configured': True}
2025-08-20 21:42:39 | INFO     | app.main:validate_system_config:154 | ✅ 多模态服务验证完成
2025-08-20 21:42:39 | INFO     | app.main:init_databases:164 | 初始化数据库连接...
2025-08-20 21:42:39 | INFO     | app.core.database_startup:initialize_database_on_startup:26 | 🚀 开始初始化数据库...
2025-08-20 21:42:39 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/ai_automation
2025-08-20 21:42:39 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
